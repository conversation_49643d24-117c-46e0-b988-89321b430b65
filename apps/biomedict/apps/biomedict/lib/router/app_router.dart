import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:auth/auth.dart';
import '../screens/home/<USER>';

class AppRouter {
  static GoRouter createRouter() {
    final authStore = AuthDI.authStore;

    return GoRouter(
      initialLocation: '/home',
      refreshListenable: authStore,
      redirect: AuthRouteGuards.authRedirect,
      routes: [
        // Root redirect
        GoRoute(path: '/', redirect: (context, state) => '/home'),

        // Auth routes from auth package
        ...AuthRouter.routes,

        // App-specific routes
        GoRoute(
          path: '/home',
          name: 'home',
          builder: (context, state) => AuthGuard(child: const HomeScreen()),
        ),

        // Add more app routes here as needed
      ],
      errorBuilder:
          (context, state) => Scaffold(
            appBar: AppBar(title: const Text('Error')),
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 64, color: Colors.red),
                  const SizedBox(height: 16),
                  Text(
                    'Page not found: ${state.matchedLocation}',
                    style: const TextStyle(fontSize: 18),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => context.go('/home'),
                    child: const Text('Go Home'),
                  ),
                ],
              ),
            ),
          ),
    );
  }
}
