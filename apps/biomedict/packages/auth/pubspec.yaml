name: auth
description: "Authentication package with MobX state management and Firebase integration for BiomeDict monorepo."
version: 0.0.1
homepage:

environment:
  sdk: ^3.7.2
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter

  # State Management
  mobx: ^2.3.3+2
  flutter_mobx: ^2.2.1+1

  # Firebase Authentication
  firebase_auth: ^5.6.0
  firebase_ui_auth: ^1.17.0
  firebase_core: ^3.14.0

  # Dependency Injection
  get_it: ^8.0.2

  # Navigation
  go_router: ^15.2.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # Code Generation
  build_runner: ^2.4.13
  mobx_codegen: ^2.6.1

  # Testing
  mockito: ^5.4.4

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # To add assets to your package, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/to/asset-from-package
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/to/font-from-package
