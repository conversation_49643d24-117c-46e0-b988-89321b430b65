# Auth Package

A comprehensive authentication package for Flutter applications using Firebase Authentication with MobX state management. This package provides a complete authentication solution with proper separation of concerns, dependency injection, and modern UI components.

[![nonstop_cli](https://img.shields.io/badge/started%20with-nonstop_cli-166C4E.svg?style=flat-square)](https://pub.dev/packages/nonstop_cli)
[![melos](https://img.shields.io/badge/maintained%20with-melos-f700ff.svg?style=flat-square)](https://github.com/invertase/melos)

## Features

- 🔐 **Firebase Authentication Integration** - Complete Firebase Auth wrapper with error handling
- 📱 **Firebase UI Auth Components** - Pre-built, customizable authentication screens
- 🔄 **MobX State Management** - Reactive state management for authentication state
- 💉 **Dependency Injection** - Clean architecture with GetIt service locator
- 🛡️ **Route Guards** - Protect routes based on authentication state
- ✅ **Email Verification** - Built-in email verification flow
- 🎨 **Customizable UI** - Flexible authentication components
- 🧪 **Testable Architecture** - Clean separation of concerns for easy testing

## Architecture

The package follows clean architecture principles with clear separation of concerns:

```
lib/
├── src/
│   ├── models/          # Data models (UserModel, AuthState, AuthResult)
│   ├── services/        # Business logic (AuthService, AuthRepository)
│   ├── stores/          # MobX stores for state management
│   ├── widgets/         # Reusable UI components
│   ├── navigation/      # Route guards and navigation helpers
│   └── di/              # Dependency injection setup
└── auth.dart           # Public API exports
```

## Getting Started

### 1. Add Dependencies

Add the auth package to your `pubspec.yaml`:

```yaml
dependencies:
  auth:
    path: ../packages/auth  # Adjust path as needed
  flutter_mobx: ^2.2.1+1
```

### 2. Initialize Firebase

Ensure Firebase is properly configured in your app. Add Firebase configuration to your `main.dart`:

```dart
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Initialize auth dependencies
  await AuthDI.initialize();

  runApp(MyApp());
}
```

### 3. Set Up Router

Configure your router with authentication guards:

```dart
import 'package:go_router/go_router.dart';
import 'package:auth/auth.dart';

class AppRouter {
  static GoRouter createRouter() {
    return GoRouter(
      initialLocation: '/home',
      refreshListenable: AuthDI.authStore,
      redirect: AuthRouteGuards.authRedirect,
      routes: [
        // Auth routes from auth package
        ...AuthRouter.routes,

        // Your app routes
        GoRoute(
          path: '/home',
          builder: (context, state) => AuthGuard(
            child: HomeScreen(),
          ),
        ),
      ],
    );
  }
}
```

## Usage

### Basic Authentication Flow

The package provides a complete authentication flow out of the box:

```dart
import 'package:auth/auth.dart';

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      routerConfig: AppRouter.createRouter(),
    );
  }
}
```

### Using AuthGuard

Protect your routes with the `AuthGuard` widget:

```dart
AuthGuard(
  requireEmailVerification: true,  // Optional: require email verification
  child: ProtectedScreen(),
  fallback: (context) => CustomUnauthenticatedWidget(),  // Optional custom fallback
)
```

### Accessing Authentication State

Use the auth store to access authentication state:

```dart
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:auth/auth.dart';

class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final authStore = AuthDI.authStore;

    return Observer(
      builder: (context) {
        if (authStore.isAuthenticated) {
          return Text('Welcome, ${authStore.user?.email}!');
        } else {
          return Text('Please sign in');
        }
      },
    );
  }
}
```

### Manual Authentication Operations

Perform authentication operations programmatically:

```dart
final authStore = AuthDI.authStore;

// Sign in
final success = await authStore.signInWithEmailAndPassword(
  email: '<EMAIL>',
  password: 'password123',
);

// Sign up
final success = await authStore.createUserWithEmailAndPassword(
  email: '<EMAIL>',
  password: 'password123',
);

// Sign out
await authStore.signOut();

// Send email verification
await authStore.sendEmailVerification();

// Reset password
await authStore.sendPasswordResetEmail(email: '<EMAIL>');
```

## API Reference

### Core Classes

- **`AuthStore`** - MobX store for authentication state management
- **`AuthService`** - Firebase Authentication service wrapper
- **`AuthRepository`** - Repository pattern for authentication operations
- **`UserModel`** - User data model
- **`AuthResult<T>`** - Result wrapper for authentication operations

### Widgets

- **`AuthWrapper`** - Main authentication wrapper component
- **`AuthGuard`** - Route protection widget

### Navigation

- **`AuthRouteGuards`** - Route guard functions for go_router
- **`AuthRouter`** - Pre-configured authentication routes

## Contributing

This package is part of the BiomeDict monorepo. To contribute:

1. Follow the existing code style and architecture
2. Add tests for new features
3. Update documentation as needed
4. Ensure all existing tests pass

## License

This package is part of the BiomeDict project and follows the same license terms.