import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:firebase_ui_auth/firebase_ui_auth.dart' as ui_auth;
import '../widgets/auth_guard.dart';

/// Authentication-specific routes for go_router
class AuthRouter {
  /// Get authentication routes
  static List<RouteBase> get routes => [
    GoRoute(
      path: '/auth',
      redirect: (context, state) => '/auth/signin',
    ),
    GoRoute(
      path: '/auth/signin',
      name: 'signin',
      builder: (context, state) => ui_auth.SignInScreen(
        providers: [
          ui_auth.EmailAuthProvider(),
        ],
        headerBuilder: (context, constraints, shrinkOffset) {
          return const _AuthHeader();
        },
        footerBuilder: (context, constraints) {
          return const _AuthFooter();
        },
        actions: [
          ui_auth.AuthStateChangeAction<ui_auth.SignedIn>((context, state) {
            context.go('/home');
          }),
          ui_auth.ForgotPasswordAction((context, email) {
            context.push('/auth/forgot-password', extra: email);
          }),
        ],
      ),
    ),
    GoRoute(
      path: '/auth/signup',
      name: 'signup',
      builder: (context, state) => ui_auth.RegisterScreen(
        providers: [
          ui_auth.EmailAuthProvider(),
        ],
        headerBuilder: (context, constraints, shrinkOffset) {
          return const _AuthHeader();
        },
        footerBuilder: (context, constraints) {
          return const _AuthFooter();
        },
        actions: [
          ui_auth.AuthStateChangeAction<ui_auth.UserCreated>((context, state) {
            context.go('/auth/verify-email');
          }),
        ],
      ),
    ),
    GoRoute(
      path: '/auth/forgot-password',
      name: 'forgot-password',
      builder: (context, state) {
        final email = state.extra as String?;
        return ui_auth.ForgotPasswordScreen(
          email: email,
          headerBuilder: (context, constraints, shrinkOffset) {
            return const _AuthHeader();
          },
        );
      },
    ),
    GoRoute(
      path: '/auth/verify-email',
      name: 'verify-email',
      builder: (context, state) => ui_auth.EmailVerificationScreen(
        headerBuilder: (context, constraints, shrinkOffset) {
          return const _AuthHeader();
        },
        actions: [
          ui_auth.EmailLinkSignInAction((context) {
            context.go('/home');
          }),
          ui_auth.AuthCancelledAction((context) {
            context.go('/auth/signin');
          }),
        ],
      ),
    ),
    GoRoute(
      path: '/auth/profile',
      name: 'profile',
      builder: (context, state) => AuthGuard(
        requireEmailVerification: true,
        child: ui_auth.ProfileScreen(
          providers: [
            ui_auth.EmailAuthProvider(),
          ],
          actions: [
            ui_auth.SignedOutAction((context) {
              context.go('/auth/signin');
            }),
          ],
        ),
      ),
    ),
  ];
}

/// Header widget for auth screens
class _AuthHeader extends StatelessWidget {
  const _AuthHeader();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        children: [
          const Icon(
            Icons.medical_services,
            size: 80,
            color: Colors.deepPurple,
          ),
          const SizedBox(height: 16),
          const Text(
            'BiomeDict',
            style: TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: Colors.deepPurple,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Your Medical Dictionary',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
}

/// Footer widget for auth screens
class _AuthFooter extends StatelessWidget {
  const _AuthFooter();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TextButton(
                onPressed: () => context.go('/privacy'),
                child: const Text('Privacy Policy'),
              ),
              const Text(' • '),
              TextButton(
                onPressed: () => context.go('/terms'),
                child: const Text('Terms of Service'),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '© 2024 BiomeDict. All rights reserved.',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
}
