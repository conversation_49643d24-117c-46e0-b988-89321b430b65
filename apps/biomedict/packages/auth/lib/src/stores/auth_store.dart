import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:mobx/mobx.dart';
import '../models/auth_result.dart';
import '../models/auth_state.dart';
import '../models/user_model.dart';
import '../services/auth_repository.dart';

part 'auth_store.g.dart';

/// MobX store for managing authentication state
class AuthStore = AuthStoreBase with _$AuthStore;

abstract class AuthStoreBase with Store, ChangeNotifier {
  final AuthRepository _authRepository;
  StreamSubscription<UserModel?>? _authSubscription;

  AuthStoreBase(this._authRepository) {
    _initializeAuthListener();
  }

  // Observable state
  @observable
  UserModel? user;

  @observable
  AuthState authState = AuthState.loading;

  @observable
  bool isLoading = false;

  @observable
  String? errorMessage;

  @observable
  String? successMessage;

  // Computed properties
  @computed
  bool get isAuthenticated =>
      authState == AuthState.authenticated && user != null;

  @computed
  bool get isUnauthenticated => authState == AuthState.unauthenticated;

  @computed
  bool get hasError => authState == AuthState.error && errorMessage != null;

  @computed
  bool get isEmailVerified => user?.emailVerified ?? false;

  @computed
  String get displayName => user?.displayName ?? user?.email ?? 'User';

  // Actions
  @action
  void _initializeAuthListener() {
    _authSubscription = _authRepository.authStateChanges.listen((
      UserModel? user,
    ) {
      runInAction(() {
        this.user = user;
        authState =
            user != null ? AuthState.authenticated : AuthState.unauthenticated;
        if (user != null) {
          errorMessage = null;
        }
        notifyListeners();
      });
    });
  }

  @action
  void _setLoading(bool loading) {
    isLoading = loading;
    notifyListeners();
  }

  @action
  void _setError(String? error) {
    errorMessage = error;
    successMessage = null;
    authState = error != null ? AuthState.error : authState;
    notifyListeners();
  }

  @action
  void _setSuccess(String? message) {
    successMessage = message;
    errorMessage = null;
    notifyListeners();
  }

  @action
  void clearMessages() {
    errorMessage = null;
    successMessage = null;
    notifyListeners();
  }

  @action
  Future<bool> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      _setLoading(true);
      clearMessages();

      final result = await _authRepository.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      return result.fold(
        (user) {
          _setSuccess(result.data != null ? 'Successfully signed in' : null);
          return true;
        },
        (message, code) {
          _setError(message);
          return false;
        },
      );
    } finally {
      _setLoading(false);
    }
  }

  @action
  Future<bool> createUserWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      _setLoading(true);
      clearMessages();

      final result = await _authRepository.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      return result.fold(
        (user) {
          _setSuccess('Account created successfully');
          return true;
        },
        (message, code) {
          _setError(message);
          return false;
        },
      );
    } finally {
      _setLoading(false);
    }
  }

  @action
  Future<bool> signOut() async {
    try {
      _setLoading(true);
      clearMessages();

      final result = await _authRepository.signOut();

      return result.fold(
        (_) {
          _setSuccess('Successfully signed out');
          return true;
        },
        (message, code) {
          _setError(message);
          return false;
        },
      );
    } finally {
      _setLoading(false);
    }
  }

  @action
  Future<bool> sendPasswordResetEmail({required String email}) async {
    try {
      _setLoading(true);
      clearMessages();

      final result = await _authRepository.sendPasswordResetEmail(email: email);

      return result.fold(
        (_) {
          _setSuccess('Password reset email sent');
          return true;
        },
        (message, code) {
          _setError(message);
          return false;
        },
      );
    } finally {
      _setLoading(false);
    }
  }

  @action
  Future<bool> sendEmailVerification() async {
    try {
      _setLoading(true);
      clearMessages();

      final result = await _authRepository.sendEmailVerification();

      return result.fold(
        (_) {
          _setSuccess('Verification email sent');
          return true;
        },
        (message, code) {
          _setError(message);
          return false;
        },
      );
    } finally {
      _setLoading(false);
    }
  }

  @action
  Future<bool> reloadUser() async {
    try {
      _setLoading(true);
      clearMessages();

      final result = await _authRepository.reloadUser();

      return result.fold(
        (updatedUser) {
          runInAction(() {
            user = updatedUser;
          });
          return true;
        },
        (message, code) {
          _setError(message);
          return false;
        },
      );
    } finally {
      _setLoading(false);
    }
  }

  @action
  Future<bool> updateDisplayName(String displayName) async {
    try {
      _setLoading(true);
      clearMessages();

      final result = await _authRepository.updateDisplayName(displayName);

      return result.fold(
        (updatedUser) {
          runInAction(() {
            user = updatedUser;
          });
          _setSuccess('Display name updated');
          return true;
        },
        (message, code) {
          _setError(message);
          return false;
        },
      );
    } finally {
      _setLoading(false);
    }
  }

  @action
  Future<bool> deleteAccount() async {
    try {
      _setLoading(true);
      clearMessages();

      final result = await _authRepository.deleteAccount();

      return result.fold(
        (_) {
          _setSuccess('Account deleted successfully');
          return true;
        },
        (message, code) {
          _setError(message);
          return false;
        },
      );
    } finally {
      _setLoading(false);
    }
  }

  /// Dispose resources
  @override
  void dispose() {
    _authSubscription?.cancel();
    super.dispose();
  }
}
