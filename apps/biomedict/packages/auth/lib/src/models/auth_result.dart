/// Generic result wrapper for authentication operations
sealed class AuthResult<T> {
  const AuthResult();
}

/// Represents a successful authentication operation
class AuthSuccess<T> extends AuthResult<T> {
  final T data;
  final String? message;

  const AuthSuccess(this.data, {this.message});

  @override
  String toString() => 'AuthSuccess(data: $data, message: $message)';
}

/// Represents a failed authentication operation
class AuthFailure<T> extends AuthResult<T> {
  final String message;
  final String? code;
  final Exception? exception;

  const AuthFailure(this.message, {this.code, this.exception});

  @override
  String toString() => 'AuthFailure(message: $message, code: $code)';
}

/// Extension methods for AuthResult
extension AuthResultExtension<T> on AuthResult<T> {
  /// Returns true if the result is successful
  bool get isSuccess => this is AuthSuccess<T>;

  /// Returns true if the result is a failure
  bool get isFailure => this is AuthFailure<T>;

  /// Returns the data if successful, null otherwise
  T? get data => switch (this) {
    AuthSuccess<T> success => success.data,
    AuthFailure<T> _ => null,
  };

  /// Returns the error message if failed, null otherwise
  String? get errorMessage => switch (this) {
    AuthSuccess<T> _ => null,
    AuthFailure<T> failure => failure.message,
  };

  /// Returns the error code if failed, null otherwise
  String? get errorCode => switch (this) {
    AuthSuccess<T> _ => null,
    AuthFailure<T> failure => failure.code,
  };

  /// Executes onSuccess if result is successful, onFailure otherwise
  R fold<R>(
    R Function(T data) onSuccess,
    R Function(String message, String? code) onFailure,
  ) {
    return switch (this) {
      AuthSuccess<T> success => onSuccess(success.data),
      AuthFailure<T> failure => onFailure(failure.message, failure.code),
    };
  }

  /// Maps the data if successful, returns failure otherwise
  AuthResult<R> map<R>(R Function(T data) mapper) {
    return switch (this) {
      AuthSuccess<T> success => AuthSuccess(mapper(success.data)),
      AuthFailure<T> failure => AuthFailure(
        failure.message,
        code: failure.code,
        exception: failure.exception,
      ),
    };
  }
}
