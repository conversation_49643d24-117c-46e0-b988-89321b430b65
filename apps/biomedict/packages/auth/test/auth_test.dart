import 'package:flutter_test/flutter_test.dart';
import 'package:auth/auth.dart';

void main() {
  group('Auth Package Tests', () {
    test('AuthState enum values', () {
      expect(AuthState.authenticated.isAuthenticated, true);
      expect(AuthState.unauthenticated.isUnauthenticated, true);
      expect(AuthState.loading.isLoading, true);
      expect(AuthState.error.hasError, true);
    });

    test('AuthResult success', () {
      const result = AuthSuccess('test data');
      expect(result.isSuccess, true);
      expect(result.isFailure, false);
      expect(result.data, 'test data');
    });

    test('AuthResult failure', () {
      const result = AuthFailure('error message');
      expect(result.isSuccess, false);
      expect(result.isFailure, true);
      expect(result.errorMessage, 'error message');
    });
  });
}
